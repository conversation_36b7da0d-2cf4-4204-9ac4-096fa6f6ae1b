"use client";

import { siteConfig } from "@/config/site";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { HamburgerMenuIcon, PersonIcon, ExitIcon } from "@radix-ui/react-icons";
import { NavItem } from "@/types/nav";
import { signOut } from "next-auth/react";
import { MarketStatusIndicator } from "@/app/_components/market-status-indicator";

function Header() {
  return (
    <header className="bg-slate-50 dark:bg-slate-900 sticky top-0 z-40 w-full border-b">
      <div className="flex h-16 items-center justify-between px-4">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon">
              <HamburgerMenuIcon className="h-6 w-6" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-[240px] sm:w-[300px]">
            <SheetTitle className="sr-only">Main Menu</SheetTitle>
            <nav className="flex flex-col space-y-1 mt-6">
              {siteConfig.mainNav.map((item: NavItem, index) => (
                <a
                  key={index}
                  href={item.href}
                  className="group flex items-center rounded-lg px-3 py-2.5 text-sm font-medium transition-all hover:bg-slate-100 dark:hover:bg-slate-800"
                >
                  <span className="flex items-center gap-3">
                    {item.icon && (
                      <span className="text-slate-500 group-hover:text-slate-900 dark:text-slate-400 dark:group-hover:text-slate-50">
                        <item.icon />
                      </span>
                    )}
                    <span className="text-slate-700 group-hover:text-slate-900 dark:text-slate-300 dark:group-hover:text-slate-50">
                      {item.title}
                    </span>
                  </span>
                  <span
                    className="ml-auto opacity-0 transition-opacity group-hover:opacity-100"
                    aria-hidden="true"
                  >
                    &rarr;
                  </span>
                </a>
              ))}
            </nav>
          </SheetContent>
        </Sheet>

        <div className="flex items-center gap-4">
          <MarketStatusIndicator />
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <PersonIcon className="h-6 w-6" />
                <span className="sr-only">Open profile menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-full sm:w-[400px]">
              <SheetTitle className="sr-only">Profile Menu</SheetTitle>
              <nav className="flex flex-col space-y-4 mt-6">
                <div className="text-lg font-medium text-slate-900 px-3 mb-2">
                  My Account
                </div>
                <a
                  href="/profile"
                  className="group flex items-center rounded-lg px-3 py-2.5 text-sm font-medium transition-all hover:bg-slate-100"
                >
                  <span className="flex items-center gap-3">
                    <span className="text-slate-500 group-hover:text-slate-900">
                      <PersonIcon className="h-5 w-5" />
                    </span>
                    <span className="text-slate-700 group-hover:text-slate-900">
                      Profile
                    </span>
                  </span>
                </a>
                <button
                  onClick={() => signOut({ callbackUrl: "/" })}
                  className="group flex w-full items-center rounded-lg px-3 py-2.5 text-sm font-medium transition-all hover:bg-slate-100"
                >
                  <span className="flex items-center gap-3">
                    <span className="text-slate-500 group-hover:text-slate-900">
                      <ExitIcon className="h-5 w-5" />
                    </span>
                    <span className="text-slate-700 group-hover:text-slate-900">
                      Logout
                    </span>
                  </span>
                </button>
              </nav>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}

export default Header;
