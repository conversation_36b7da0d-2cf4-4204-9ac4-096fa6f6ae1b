"use client";

import { Theme } from "@radix-ui/themes";
import { NextThemeProvider } from "@/providers/next-theme-provider";
import { NextAuthSessionProvider } from "@/providers/session-provider";
import Header from "@/components/header";
import { FooterNav } from "@/components/footer-nav";
import dynamic from "next/dynamic";
import React from "react";

// Dynamically import Eruda with no SSR
const ErudaDebug = dynamic(
  () => import("./eruda-debug").then((mod) => mod.ErudaDebug),
  { ssr: false },
);

export function RootLayoutClient({ children }: { children: React.ReactNode }) {
  return (
    <NextThemeProvider>
      <NextAuthSessionProvider>
        <Theme
          appearance="light"
          accentColor="mint"
          radius="large"
          scaling="110%"
        >
          {process.env.NODE_ENV === "development" && (
            <React.Suspense fallback={null}>
              <ErudaDebug />
            </React.Suspense>
          )}
          <div className="flex min-h-screen flex-col">
            <Header />
            <main className="flex-1">{children}</main>
            <FooterNav />
          </div>
        </Theme>
      </NextAuthSessionProvider>
    </NextThemeProvider>
  );
}
