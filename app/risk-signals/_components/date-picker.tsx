"use client";

import * as React from "react";
import { format, isWeekend } from "date-fns";
import { Calendar as CalendarIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DatePickerProps {
  date: Date;
  onSelect: (date: Date | undefined) => void;
}

export function DatePicker({ date, onSelect }: DatePickerProps) {
  const isDateDisabled = (date: Date) => {
    return date > new Date() || isWeekend(date);
  };

  const handleDateSelect = (selectedDate: Date | undefined) => {
    if (selectedDate) {
      // Create date in UTC to avoid timezone offset issues
      const year = selectedDate.getFullYear();
      const month = selectedDate.getMonth();
      const day = selectedDate.getDate();

      // Create new UTC date
      const normalizedDate = new Date(Date.UTC(year, month, day, 12, 0, 0));

      onSelect(normalizedDate);
    } else {
      onSelect(undefined);
    }
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="relative group h-11 px-4 bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 hover:border-blue-400 dark:hover:border-blue-500 transition-colors"
        >
          <div className="flex items-center justify-center gap-2">
            <CalendarIcon className="h-4 w-4 text-slate-500 dark:text-slate-400" />
            <span className="font-medium text-slate-900 dark:text-white">
              {format(date, "MMM dd, yyyy")}
            </span>
          </div>
          <span className="sr-only">Open date picker</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0 bg-white dark:bg-slate-900 border-slate-200 dark:border-slate-700 shadow-xl" align="end">
        <Calendar
          mode="single"
          selected={date}
          onSelect={handleDateSelect}
          disabled={isDateDisabled}
          initialFocus
          className="bg-white dark:bg-slate-900"
        />
      </PopoverContent>
    </Popover>
  );
}
